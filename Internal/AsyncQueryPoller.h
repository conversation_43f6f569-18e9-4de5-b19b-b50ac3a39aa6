#pragma once
#include "../mimalloc_integration.h"
#include "../AsyncQueryExecutor.h"
#include <thread>
#include <mutex>
#include <atomic>
#include <functional>

namespace Database
{
    // 비동기 쿼리 폴링 관리자 (내부 컴포넌트)
    class AsyncQueryPoller
    {
    public:
        AsyncQueryPoller();
        ~AsyncQueryPoller();
        
        // 폴링 시작
        bool Start();
        
        // 폴링 중지
        void Stop();
        
        // 비동기 쿼리 추가
        void AddQuery(AsyncQueryTask task);
        
        // 활성 쿼리 수
        size_t GetActiveQueryCount() const;
        
        // 완료 콜백 설정
        void SetCompletionCallback(std::function<void(AsyncQueryTask&)> callback)
        {
            m_completionCallback = callback;
        }
        
    private:
        // 폴링 스레드 함수
        void PollingThreadFunc();
        
        // 완료된 작업 처리
        void ProcessCompletedTasks(mi::vector<AsyncQueryTask>& completedTasks);
        
        // 활성 비동기 쿼리들
        mi::vector<AsyncQueryTask> m_activeQueries;  // mimalloc 사용
        mutable std::mutex m_activeQueriesMutex;
        
        // 폴링 스레드
        std::thread m_pollingThread;
        std::atomic<bool> m_pollingRunning{false};
        std::atomic<bool> m_stopRequested{false};
        
        // 비동기 쿼리 실행기
        std::unique_ptr<AsyncQueryExecutor> m_asyncExecutor;
        
        // 완료 콜백
        std::function<void(AsyncQueryTask&)> m_completionCallback;
    };
}