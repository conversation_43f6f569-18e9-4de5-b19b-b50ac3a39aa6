#include "stdafx.h"
#include "WorkerThreadPool.h"
#include "../Diagnostics/NSLogger.h"

namespace Database
{

WorkerThreadPool::~WorkerThreadPool()
{
    Stop();
}

bool WorkerThreadPool::Start(uint32_t threadCount)
{
    if (m_running.load())
    {
        LOGW << "WorkerThreadPool already running";
        return false;
    }
    
    m_running = true;
    m_stopRequested = false;
    
    // 워커 스레드 생성
    m_workerThreads.reserve(threadCount);
    for (uint32_t i = 0; i < threadCount; ++i)
    {
        m_workerThreads.emplace_back(&WorkerThreadPool::WorkerThreadFunc, this);
    }
    
    LOGI << "WorkerThreadPool started with " << threadCount << " threads";
    return true;
}

void WorkerThreadPool::Stop()
{
    if (!m_running.load())
        return;
    
    // 중지 요청
    m_stopRequested = true;
    
    // 모든 워커 스레드 종료 대기
    for (auto& thread : m_workerThreads)
    {
        if (thread.joinable())
        {
            thread.join();
        }
    }
    
    m_workerThreads.clear();
    m_running = false;
    
    // 남은 작업 정리
    std::function<void()> work;
    while (m_workQueue.try_dequeue(work))
    {
        // Just dequeue to clear
    }
    
    LOGI << "WorkerThreadPool stopped";
}

void WorkerThreadPool::PostWork(std::function<void()> work)
{
    if (!work || !m_running.load())
        return;
    
    m_workQueue.enqueue(std::move(work));
}

size_t WorkerThreadPool::GetPendingWorkCount() const
{
    return m_workQueue.size_approx();
}

void WorkerThreadPool::WorkerThreadFunc()
{
    while (!m_stopRequested.load())
    {
        std::function<void()> work;
        
        // 100ms 타임아웃으로 대기 (graceful shutdown을 위해)
        if (m_workQueue.wait_dequeue_timed(work, std::chrono::milliseconds(100)))
        {
            // 작업 실행
            try
            {
                work();
            }
            catch (const std::exception& e)
            {
                LOGE << "Worker thread exception: " << e.what();
            }
            catch (...)
            {
                LOGE << "Worker thread unknown exception";
            }
        }
    }
}

} // namespace Database