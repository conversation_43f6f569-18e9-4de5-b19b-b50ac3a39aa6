#include "stdafx.h"
#include "AsyncQueryPoller.h"
#include "../Diagnostics/NSLogger.h"

namespace Database
{

AsyncQueryPoller::AsyncQueryPoller()
    : m_asyncExecutor(std::make_unique<AsyncQueryExecutor>())
{
}

AsyncQueryPoller::~AsyncQueryPoller()
{
    Stop();
}

bool AsyncQueryPoller::Start()
{
    if (m_pollingRunning.load())
    {
        LOGW << "AsyncQueryPoller already running";
        return false;
    }
    
    m_pollingRunning = true;
    m_stopRequested = false;
    
    // 폴링 스레드 시작
    m_pollingThread = std::thread(&AsyncQueryPoller::PollingThreadFunc, this);
    
    LOGI << "AsyncQueryPoller started";
    return true;
}

void AsyncQueryPoller::Stop()
{
    if (!m_pollingRunning.load())
        return;
    
    LOGI << "AsyncQueryPoller stopping, waiting for " << GetActiveQueryCount() << " active queries";
    
    // 중지 요청 (새 쿼리 추가 차단)
    m_stopRequested = true;
    
    // 남은 쿼리 완료 대기 (최대 1분)
    auto startTime = std::chrono::steady_clock::now();
    const auto maxWaitTime = std::chrono::seconds(60);
    
    while (!m_activeQueries.empty())
    {
        auto elapsed = std::chrono::steady_clock::now() - startTime;
        if (elapsed > maxWaitTime)
        {
            LOGW << "Timeout waiting for queries to complete, " 
                 << m_activeQueries.size() << " queries still executing";
            break;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // 폴링 스레드 종료 대기
    if (m_pollingThread.joinable())
    {
        m_pollingThread.join();
    }
    
    m_pollingRunning = false;
    
    // 1분 후에도 완료되지 않은 쿼리들 강제 실패 처리
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    if (!m_activeQueries.empty())
    {
        LOGW << "Force failing " << m_activeQueries.size() << " uncompleted queries";
        for (auto& task : m_activeQueries)
        {
            task.state = AsyncQueryState::Failed;
            task.errorMessage = "Query cancelled - shutdown timeout";
            
            if (m_completionCallback)
            {
                m_completionCallback(task);
            }
            
            m_asyncExecutor->CleanupTask(task);
        }
        m_activeQueries.clear();
    }
    
    LOGI << "AsyncQueryPoller stopped";
}

void AsyncQueryPoller::AddQuery(AsyncQueryTask task)
{
    // 종료 중이면 즉시 실패 처리
    if (m_stopRequested.load())
    {
        task.state = AsyncQueryState::Failed;
        task.errorMessage = "AsyncQueryPoller is shutting down";
        if (m_completionCallback)
        {
            m_completionCallback(task);
        }
        return;
    }
    
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    m_activeQueries.push_back(std::move(task));
}

size_t AsyncQueryPoller::GetActiveQueryCount() const
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    return m_activeQueries.size();
}

void AsyncQueryPoller::PollingThreadFunc()
{
    while (!m_stopRequested.load())
    {
        mi::vector<AsyncQueryTask> completedTasks;
        
        // 활성 쿼리 처리
        {
            std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
            
            for (auto it = m_activeQueries.begin(); it != m_activeQueries.end();)
            {
                auto& task = *it;
                bool taskCompleted = false;
                
                // 타임아웃 체크
                if (m_asyncExecutor->IsTimedOut(task))
                {
                    LOGW << "Query timed out for CID " << task.cid << ", procedure: " << task.procedureName;
                    task.state = AsyncQueryState::Failed;
                    task.errorMessage = "Query execution timed out";
                    taskCompleted = true;
                }
                else
                {
                    switch (task.state)
                    {
                    case AsyncQueryState::Executing:
                        if (m_asyncExecutor->ContinueAsyncQuery(task))
                        {
                            // ContinueAsyncQuery가 true를 반환하면 완료 또는 실패
                            taskCompleted = true;
                        }
                        break;
                        
                    case AsyncQueryState::Completed:
                    case AsyncQueryState::Failed:
                        taskCompleted = true;
                        break;
                    }
                }
                
                if (taskCompleted)
                {
                    completedTasks.push_back(std::move(task));
                    it = m_activeQueries.erase(it);
                }
                else
                {
                    ++it;
                }
            }
        }
        
        // 완료된 작업 처리
        ProcessCompletedTasks(completedTasks);
        
        // 폴링 간격
        if (m_activeQueries.empty())
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(50));  // 쿼리 없을 때
        }
        else
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));  // 쿼리 있을 때
        }
    }
}

void AsyncQueryPoller::ProcessCompletedTasks(mi::vector<AsyncQueryTask>& completedTasks)
{
    for (auto& task : completedTasks)
    {
        try
        {
            if (m_completionCallback)
            {
                m_completionCallback(task);
            }
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception in completion callback for CID " << task.cid << ": " << e.what();
        }
        
        // 정리
        m_asyncExecutor->CleanupTask(task);
    }
}

} // namespace Database