#pragma once
#include <memory>
#include <vector>
#include <mutex>
#include <atomic>
#include "../ConnectionInfo.h"
#include "../db_concurrentqueue.h"

// Forward declaration
class NSMySQLConnection;

// MariaDB 연결 풀 - 스레드 안전성 개선 버전
// 필수 규칙: 같은 CID는 항상 같은 DB 커넥션 사용
class NSMySQLConnectionPool
{
public:
    NSMySQLConnectionPool();
    NSMySQLConnectionPool(const std::string& host, int port, const std::string& dbName,
                         const std::string& user, const std::string& password);
    ~NSMySQLConnectionPool();

    // 복사/이동 방지
    NSMySQLConnectionPool(const NSMySQLConnectionPool&) = delete;
    NSMySQLConnectionPool& operator=(const NSMySQLConnectionPool&) = delete;
    NSMySQLConnectionPool(NSMySQLConnectionPool&&) = delete;
    NSMySQLConnectionPool& operator=(NSMySQLConnectionPool&&) = delete;

    // 초기화/종료
    bool Initialize(int databaseType, int shardId);
    void Finalize();

    // 연결 획득/반환
    std::shared_ptr<NSMySQLConnection> GetConnection();  // 있으면 반환, 없으면 nullptr
    void ReturnConnection(std::shared_ptr<NSMySQLConnection> conn);
    
    // 대기 작업 등록
    void RegisterWaitingTask(int64_t cid, 
        std::function<void(std::shared_ptr<NSMySQLConnection>)> callback);
    
    // 연결 정보 반환 (ConnectionManager용)
    Database::ConnectionInfo GetConnectionInfo() const;

    // 설정
    void SetMinConnections(int count) { m_minConnections = count; }
    void SetMaxConnections(int count) { m_maxConnections = count; }
    
    // 연결 정보 추가
    bool AddConnectionInfo(const std::string& host, int port, const std::string& dbName, 
                          const std::string& user, const std::string& password);
    
    // 재연결
    void Reconnect();
    
    // 상태 조회
    int GetActiveConnections() const { return m_connections.size(); }
    int GetTotalConnections() const { return m_connections.size(); }
    int GetMaxConnections() const { return m_maxConnections; }
    bool IsHealthy() const;

private:
    // 연결 생성
    std::shared_ptr<NSMySQLConnection> CreateConnection();
    
    // 연결 정보 로드
    bool LoadConnectionInfo();
    
    // 연결 검증
    bool ValidateConnection(const std::shared_ptr<NSMySQLConnection>& conn);

    // 연결 관리
    struct ConnectionEntry {
        std::shared_ptr<NSMySQLConnection> connection;
        std::atomic<bool> inUse{false};
    };
    std::vector<std::unique_ptr<ConnectionEntry>> m_connections;
    mutable std::mutex m_connectionsMutex;
    std::condition_variable m_connectionAvailable;

    // 설정
    int m_databaseType = 0;
    int m_shardId = 0;
    std::atomic<int> m_minConnections{5};
    std::atomic<int> m_maxConnections{20};
    
    // 통계
    std::atomic<int64_t> m_totalQueries{0};
    std::atomic<int64_t> m_failedConnections{0};
    
    // 연결 정보
    std::string m_host;
    int m_port = 3306;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    
    // 상태
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_shutting_down{false};
    
    // 대기 작업 관리
    struct WaitingTask {
        int64_t cid;
        std::function<void(std::shared_ptr<NSMySQLConnection>)> callback;
    };
    Database::ConcurrentQueue<WaitingTask> m_waitingTasks;
};