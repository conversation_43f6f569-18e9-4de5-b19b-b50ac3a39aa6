#include "../stdafx.h"
#include "NSStoredProcedureBatch.h"
#include "../Connection/NSMySQLConnection.h"
#include "../NSQueryData.h"
#include "../QueryData/NSDataSerializer.h"

std::shared_ptr<NSQueryData> NSStoredProcedureBatch::ExecuteBatch(
    NSMySQLConnection* connection,
    const NSDataSerializer& serializer)
{
    auto queryData = std::make_shared<NSQueryData>();
    
    if (!connection || !IsValid())
    {
        queryData->SetError(-1, "Invalid connection or empty batch");
        return queryData;
    }
    
    // 트랜잭션 시작
    if (!connection->BeginTransaction())
    {
        queryData->SetError(-1, "Failed to begin transaction");
        return queryData;
    }
    
    bool success = true;
    std::string lastError;
    
    // 모든 프로시저 실행
    for (auto& procedure : m_procedures)
    {
        try
        {
            auto result = procedure->Execute(connection, serializer);
            
            if (result && result->HasError())
            {
                success = false;
                lastError = result->GetErrorMessage();
                break;
            }
        }
        catch (const std::exception& e)
        {
            success = false;
            lastError = e.what();
            break;
        }
    }
    
    // 트랜잭션 커밋 또는 롤백
    if (success)
    {
        if (connection->CommitTransaction())
        {
            queryData->SetSuccess();
        }
        else
        {
            queryData->SetError(-1, "Failed to commit transaction");
            connection->RollbackTransaction();
        }
    }
    else
    {
        connection->RollbackTransaction();
        queryData->SetError(-1, lastError);
    }
    
    return queryData;
}