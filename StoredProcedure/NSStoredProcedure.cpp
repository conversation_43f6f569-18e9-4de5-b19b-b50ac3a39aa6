#include "../stdafx.h"
#include "NSStoredProcedure.h"
#include "../MySQLCommand.h"
#include "../Connection/NSMySQLConnection.h"
#include "../NSQueryData.h"
#include "../QueryData/NSDataSerializer.h"
#include "../RetryPolicy.h"

std::shared_ptr<NSQueryData> NSStoredProcedure::Execute(
    NSMySQLConnection* connection,
    const NSDataSerializer& serializer)
{
    auto queryData = std::make_shared<NSQueryData>();
    
    try
    {
        // MySQLCommand 생성
        auto command = std::make_unique<MySQLCommand>();
        
        // MakeQuery 호출하여 입력 파라미터 설정
        EErrorCode result = MakeQuery(command.get());
        if (result != EErrorCode::Success)
        {
            queryData->SetError(static_cast<int>(result), "Failed to make query");
            return queryData;
        }
        
        // 데드락 재시도 정책을 사용하여 프로시저 실행
        auto retryResult = RetryPolicy::ExecuteWithRetry([&]() {
            bool success = connection->ExecuteProcedure(GetName(), command.get());
            EErrorCode errorCode = success ? EErrorCode::None : EErrorCode::DatabaseError;
            int mysqlError = success ? 0 : connection->GetLastErrorCode();
            return std::make_tuple(success, errorCode, mysqlError);
        });
        
        if (!retryResult.value)
        {
            queryData->SetError(static_cast<int>(retryResult.errorCode), connection->GetLastError());
            queryData->SetRetryCount(retryResult.attempts);
            return queryData;
        }
        
        // MakeOutput 호출하여 출력 파라미터 가져오기
        result = MakeOutput(command.get());
        if (result != EErrorCode::Success)
        {
            queryData->SetError(static_cast<int>(result), "Failed to make output");
            return queryData;
        }
        
        // RecordSet 가져오기
        auto recordSet = connection->GetRecordSet();
        if (recordSet)
        {
            queryData->SetRecordSet(std::move(recordSet));
        }
        
        // 성공
        queryData->SetSuccess();
    }
    catch (const std::exception& e)
    {
        queryData->SetError(-1, e.what());
    }
    catch (...)
    {
        queryData->SetError(-1, "Unknown error");
    }
    
    return queryData;
}

void NSStoredProcedure::SetError(int errorCode, const std::string& errorMsg)
{
    m_errorCode = errorCode;
    m_errorMessage = errorMsg;
}

void NSStoredProcedure::LogError(const std::string& msg)
{
    // 에러 로깅 구현
    // 실제로는 로깅 시스템 사용
}