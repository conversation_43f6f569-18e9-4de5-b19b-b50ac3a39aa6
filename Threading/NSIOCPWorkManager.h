#pragma once
#include <windows.h>
#include <functional>
#include <memory>
#include <vector>
#include <atomic>

// 심플한 IOCP 기반 워커 매니저 (메모리 누수 수정 버전)
class NSIOCPWorkManager
{
public:
    using WorkFunction = std::function<void()>;

    NSIOCPWorkManager();
    ~NSIOCPWorkManager();

    // 초기화/종료
    bool Initialize(int workerCount);
    void Finalize();

    // 작업 등록
    void PostWork(WorkFunction work);

private:
    // 워커 스레드 함수
    static DWORD WINAPI WorkerThread(LPVOID param);
    void WorkerThreadMain();

    // 작업 아이템
    struct WorkItem
    {
        WorkFunction function;
        
        WorkItem() = default;
        explicit WorkItem(WorkFunction func) : function(std::move(func)) {}
    };

private:
    HANDLE m_iocp = nullptr;
    std::vector<HANDLE> m_workerThreads;
    std::atomic<bool> m_shutdown{false};
    int m_workerCount = 0;

    // 종료 완료 키
    static constexpr ULONG_PTR SHUTDOWN_KEY = 0;
    // 작업 실행 키
    static constexpr ULONG_PTR WORK_KEY = 1;
};