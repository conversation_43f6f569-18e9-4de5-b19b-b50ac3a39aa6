#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "Connection/NSMySQLConnection.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include "AsyncQueryExecutor.h"
#include "DBPerformanceMonitor.h"
#include "MySQLCommand.h"
#include <thread>
#include <sstream>
#include <mysql.h>
#include "Diagnostics/NSLogger.h"
#include "Internal/CIDQueueManager.h"
#include "Internal/WorkerThreadPool.h"
#include "Internal/AsyncQueryPoller.h"

// 시퀀스 스토리지는 이제 클래스 멤버로 관리됨 (m_sequenceShards)

// 전역 프로시저 메타데이터 캐시 정의
mi::unordered_map<std::string, std::shared_ptr<NSMySQLConnection::ProcedureMetadata>> NSDataBaseManager::s_globalMetadataCache;

void NSDataBaseManager::PostDelayedTask(int delayMs, std::function<void()> task)
{
    if (m_isShuttingDown.load() || !m_workerThreadPool)
    {
        return;
    }
    
    // 워커 스레드 풀에 지연된 작업 제출
    m_workerThreadPool->SubmitWork([delayMs, task]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(delayMs));
        
        // 실행 시점에 인스턴스와 셧다운 상태 확인
        auto* instance = NSDataBaseManager::GetInstance();
        if (instance && !instance->m_isShuttingDown.load())
        {
            task();
        }
    });
}

void NSDataBaseManager::PostWork(std::function<void()> work)
{
    if (m_isShuttingDown.load() || !m_workerThreadPool)
    {
        return;
    }
    
    m_workerThreadPool->PostWork(std::move(work));
}
std::shared_mutex NSDataBaseManager::s_globalMetadataMutex;

namespace Database
{

QueryTimer::QueryTimer(std::shared_ptr<NSQueryData> data, 
                       std::function<void(const std::shared_ptr<NSQueryData>&)> callback)
    : m_start(std::chrono::high_resolution_clock::now())
    , m_queryData(std::move(data))
    , m_callback(std::move(callback))
{
}

QueryTimer::~QueryTimer()
{
    auto elapsed = std::chrono::high_resolution_clock::now() - m_start;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    if (m_queryData)
    {
        m_queryData->SetElapsedTime(elapsedMs);
        
        if (m_callback)
        {
            try
            {
                m_callback(m_queryData);
            }
            catch (const std::exception& e)
            {
                LOGE << "Exception in QueryTimer callback: " << e.what();
            }
            catch (...)
            {
                LOGE << "Unknown exception in QueryTimer callback";
            }
        }
    }
}

} // namespace Database

// MySQL/MariaDB 라이브러리 정적 초기화
// 프로그램 시작 시 자동으로 실행되며, 모든 스레드 생성 전에 완료됨
namespace {
    class MySQLLibraryInitializer {
    private:
        bool m_initialized = false;
        
    public:
        MySQLLibraryInitializer() {
            // mysql_library_init은 스레드 안전하지 않으므로
            // main() 시작 전 정적 초기화 단계에서 실행
            if (mysql_library_init(0, nullptr, nullptr) == 0) {
                m_initialized = true;
                LOGI << "MySQL library initialized successfully";
            } else {
                // 초기화 실패 시 프로그램 종료
                // 예외를 던지면 프로그램이 시작도 못하므로 로그만 남김
                std::cerr << "FATAL: Failed to initialize MySQL library\n";
                std::abort();
            }
        }
        
        ~MySQLLibraryInitializer() {
            if (m_initialized) {
                mysql_library_end();
                LOGI << "MySQL library cleanup completed";
            }
        }
        
        // 복사/이동 방지
        MySQLLibraryInitializer(const MySQLLibraryInitializer&) = delete;
        MySQLLibraryInitializer& operator=(const MySQLLibraryInitializer&) = delete;
    };
    
    // 전역 정적 객체 - 프로그램 시작 시 자동 생성
    static MySQLLibraryInitializer g_mysqlInitializer;
}

NSDataBaseManager::NSDataBaseManager()
    : m_cidQueueManager(std::make_unique<Database::CIDQueueManager>())
    , m_workerThreadPool(std::make_unique<Database::WorkerThreadPool>())
    , m_asyncQueryPoller(std::make_unique<Database::AsyncQueryPoller>())
{
    // MySQL 라이브러리는 이미 정적으로 초기화됨
    // 추가 초기화 불필요
}

NSDataBaseManager::~NSDataBaseManager()
{
    Finalize();
    
    // MySQL 라이브러리 정리는 정적 소멸자에서 자동 처리
    // mysql_library_end() 호출 불필요
}

bool NSDataBaseManager::Initialize()
{
    if (m_initialized.exchange(true))
        return true;

    // 게임 스레드 디스패처 필수 검증
    if (!GameThreadCallback::IsDispatcherSet())
    {
        LOGE << "CRITICAL: Cannot initialize Database Manager without game thread dispatcher!";
        LOGE << "Call SetGameThreadDispatcher() BEFORE Initialize()!";
        m_initialized = false;
        return false;
    }

    try
    {
        // 비동기 쿼리 폴러 콜백 설정
        m_asyncQueryPoller->SetCompletionCallback(
            [this](Database::AsyncQueryTask& task) {
                ProcessCompletedQuery(task);
            });
        
        // 연결 풀은 AddConnectionInfo에서 생성됨

        return true;
    }
    catch (const std::exception& e)
    {
        m_initialized = false;
        return false;
    }
}

void NSDataBaseManager::Finalize()
{
    if (!m_initialized.exchange(false))
        return;

    m_isShuttingDown = true;
    Stop();

    // 폴링 스레드 종료
    // 비동기 쿼리 폴러 중지
    if (m_asyncQueryPoller)
    {
        m_asyncQueryPoller->Stop();
    }

    // 연결 풀 종료
    for (auto& pool : m_connectionPools)
    {
        if (pool)
        {
            pool->Finalize();
            pool.reset();
        }
    }
}

bool NSDataBaseManager::AddConnectionInfo(EDataBase dbType, EDBProvider provider, const std::string_view host, 
                                         uint32_t port, const std::string_view dbName, const std::string_view user, 
                                         const std::string_view password, int32_t initPoolCount)
{
    // provider 파라미터는 무시 (항상 MariaDB 사용)
    
    int32 dbTypeInt = static_cast<int32>(dbType);
    
    // 범위 체크
    if (dbTypeInt < static_cast<int32>(EDataBase::Game) || dbTypeInt >= static_cast<int32>(EDataBase::End))
        return false;
    
    // 기존 풀이 없으면 파라미터로 생성
    if (!m_connectionPools[dbTypeInt])
    {
        m_connectionPools[dbTypeInt] = std::make_unique<NSMySQLConnectionPool>(
            std::string(host), port, std::string(dbName), 
            std::string(user), std::string(password));
    }
    
    auto* pool = m_connectionPools[dbTypeInt].get();
    if (!pool)
        return false;
        
    // 풀 크기 설정
    pool->SetMaxConnections(initPoolCount);
    pool->SetMinConnections(std::min(5, initPoolCount));
    
    // 풀 초기화
    if (!pool->Initialize(dbTypeInt, 0))
    {
        // 초기화 실패 시 풀 객체 제거
        m_connectionPools[dbTypeInt].reset();
        LOGE << "Failed to initialize connection pool for dbType: " << dbTypeInt;
        return false;
    }
    
    return true;
}

NSMySQLConnectionPool* NSDataBaseManager::GetDBConnection(int32 dbType)
{
    return GetConnectionPool(dbType);
}

void NSDataBaseManager::ReconnectConnection(int32 dbType)
{
    auto* pool = GetConnectionPool(dbType);
    if (pool)
    {
        pool->Reconnect();
    }
}

int64_t NSDataBaseManager::GetDBQueueSize() const
{
    if (m_workerThreadPool)
    {
        return static_cast<int64_t>(m_workerThreadPool->GetPendingWorkCount());
    }
    return 0;
}

std::string NSDataBaseManager::GetConnectionPoolCountInfo() const
{
    std::stringstream ss;
    for (int i = 0; i < 12; ++i)
    {
        if (m_connectionPools[i])
        {
            ss << "Pool[" << i << "]: " << m_connectionPools[i]->GetActiveConnectionCount() 
               << "/" << m_connectionPools[i]->GetTotalConnectionCount() << " ";
        }
    }
    return ss.str();
}

std::string NSDataBaseManager::GetConnectionPoolCountLog() const
{
    return GetConnectionPoolCountInfo(); // 동일한 정보 반환
}

NSMySQLConnectionPool* NSDataBaseManager::GetConnectionPool(int32 dbType)
{
    if (dbType >= static_cast<int32>(EDataBase::Game) && dbType < static_cast<int32>(EDataBase::End))
        return m_connectionPools[dbType].get();

    return nullptr;
}

// 시퀀스 관리 메서드 구현 (샤드 방식)
int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    // 해당 CID의 샤드만 락 - 256개 샤드로 경쟁 확률 약 6%
    auto& shard = m_sequenceShards[GetSequenceShardIndex(cid)];
    std::lock_guard<std::mutex> lock(shard.mutex);
    return ++shard.sequences[cid];
}


template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    EDataBase dbType,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // CID 추출
        int64_t cid = 0;
        if constexpr (requires { SP::Input; })
        {
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            if constexpr (requires { input.Cid; })
                cid = input.Cid;
            else if constexpr (requires { input.Aid; })
                cid = input.Aid;
        }
        
        m_queriesProcessing++;
        m_inputCount++;
        
        // QueryData 생성
        auto queryData = std::make_shared<NSQueryData>();
        queryData->SetProcedureName(SP::GetProcedureName());
        
        // QueryTask 생성
        Database::QueryTask task{
            .dbType = dbType,
            .serializer = serializer,
            .promise = promise,
            .queryData = queryData,
            .afterExecuteCallback = m_afterExecuteQueryShared,
            .procedureName = SP::GetProcedureName(),
            .cid = cid,
            .executeFunc = [](NSMySQLConnection* conn, const NSDataSerializer& ser, std::shared_ptr<NSQueryData> data) {
                SP sp;
                auto result = sp.Execute(conn, ser);
                data->SetErrorCode(result);
            }
        };
        
        // CID 큐에 추가
        EnqueueQuery(cid, std::move(task));
        
        // Note: The actual query execution is now handled by CIDQueueManager
        // The promise will be fulfilled when the query is processed
    });
}

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
    
    // 게임 스레드 디스패처 재검증
    if (!m_gameThreadPost)
    {
        LOGE << "CRITICAL: Game thread dispatcher not properly set!";
        LOGE << "Database Manager cannot start without dispatcher.";
        return false;
    }
    
    // 연결 풀이 하나도 없으면 시작 불가
    bool hasAnyPool = false;
    for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
    {
        if (m_connectionPools[i])
        {
            hasAnyPool = true;
            break;
        }
    }
    
    if (!hasAnyPool)
    {
        LOGE << "No connection pools initialized. Call AddConnectionInfo first.";
        return false;
    }
        
    // 스레드 수 결정
    if (workThreadCnt == 0)
    {
        // 전체 연결 수 계산
        int totalConnections = 0;
        for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
        {
            if (m_connectionPools[i])
            {
                totalConnections += m_connectionPools[i]->GetMaxConnections();
            }
        }
        
        // 연결갯수 나누기 2, 최대 32개로 제한
        workThreadCnt = std::min(totalConnections / 2, 32u);
        if (workThreadCnt == 0) workThreadCnt = 1;  // 최소 1개 보장
    }
    
    m_threadCount = workThreadCnt;
    
    // 워커 스레드 풀 시작
    if (!m_workerThreadPool->Start(workThreadCnt))
    {
        LOGE << "Failed to start worker thread pool";
        return false;
    }
    
    // 비동기 쿼리 폴러 시작
    if (!m_asyncQueryPoller->Start())
    {
        LOGE << "Failed to start async query poller";
        m_workerThreadPool->Stop();
        return false;
    }
        
    // 연결 풀은 AddConnectionInfo에서 이미 초기화됨
    // 여기서는 모든 풀이 준비되었는지 확인만 함
    for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
    {
        if (m_connectionPools[i] && !m_connectionPools[i]->GetActiveConnections())
        {
            LOGW << "Connection pool " << i << " has no active connections";
        }
    }
    
    // 모든 프로시저 메타데이터 로드
    if (!LoadAllProcedureMetadata())
    {
        LOGW << "Failed to load procedure metadata, will load on demand";
        // 실패해도 계속 진행 (개별 로딩으로 fallback)
    }
        
    return true;
}

void NSDataBaseManager::Stop()
{
    ProhibitPushAccess();
    StopAllWorkerThreadAndWait();
}

void NSDataBaseManager::ProhibitPushAccess()
{
    m_pushAccessProhibit = true;
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
    // 모든 큐 작업이 완료될 때까지 대기
    while (m_pushAccessCount > 0 || m_queriesProcessing > 0)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 워커 스레드 종료
    // 워커 스레드 풀 중지
    if (m_workerThreadPool)
    {
        m_workerThreadPool->Stop();
    }
}

DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StorageUpdateQuery(
    std::shared_ptr<NSStorageUpdateContainer> containerData,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
    std::shared_ptr<NSDBSession> session,
    std::source_location location)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
        
        // CID 기반 스레드 선택
        int threadIndex = GetExecutorByShardKey(containerData->Cid);
        
        m_queriesProcessing++;
        
        // 작업 큐에 추가
        PostWork([=]() mutable
        {
            // RAII 가드로 atomic counter 보장
            struct CounterGuard {
                std::atomic<int>& queries;
                std::atomic<int>& access;
                ~CounterGuard() {
                    queries--;
                    access--;
                }
            } guard{m_queriesProcessing, m_pushAccessCount};
            
            try {
                // Query 실행
                EErrorCode queryResult = EErrorCode::None;
                if (pQueryFunc)
                {
                    queryResult = pQueryFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // Result 처리
                if (pResultFunc)
                {
                    queryResult = pResultFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);
                if (m_afterExecuteQueryShared)
                    m_afterExecuteQueryShared(queryData);
                
                promise.SetValue(queryData);
            }
            catch (const std::exception& e) {
                LOGE << "StorageUpdateQuery exception: " << e.what() 
                     << " at " << location.function_name() << ":" << location.line();
                promise.SetException(std::current_exception());
            }
            catch (...) {
                LOGE << "Unknown exception in StorageUpdateQuery"
                     << " at " << location.function_name() << ":" << location.line();
                promise.SetException(std::current_exception());
            }
        });
    });
}

// CID Queue 관리 메소드 구현
void NSDataBaseManager::EnqueueQuery(int64_t cid, Database::QueryTask task)
{
    task.cid = cid;
    
    // CID 큐 매니저에 위임
    bool isFirstTask = m_cidQueueManager->EnqueueTask(cid, std::move(task));
    
    // 첫 번째 작업이면 워커 할당
    if (isFirstTask)
    {
        m_workerThreadPool->PostWork([cid, this]() {
            auto* instance = NSDataBaseManager::GetInstance();
            if (instance && !instance->m_isShuttingDown.load()) {
                instance->ProcessCIDQueue(cid);
            }
        });
    }
}

void NSDataBaseManager::ProcessCIDQueue(int64_t cid)
{
    // CID 큐 매니저에서 다음 작업 가져오기
    Database::QueryTask task;
    if (!m_cidQueueManager->DequeueTask(cid, task))
    {
        return;  // 작업이 없음
    }
    
    // 비동기로 실행
    StartAsyncQuery(task);
    
    // 즉시 다음 CID 처리로 이동 (블로킹하지 않음!)
    return;
}

void NSDataBaseManager::StartAsyncQuery(const Database::QueryTask& task)
{
    // 연결 풀에서 가용한 연결 가져오기
    auto* pool = GetConnectionPool(static_cast<int32>(task.dbType));
    if (!pool)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get connection pool")));
        CheckNextTaskForCID(task.cid);
        return;
    }
    
    auto conn = pool->GetConnection();  // 즉시 반환
    if (!conn)
    {
        // 커넥션이 없으면 대기 큐에 등록
        LOGD << "No available connection for CID: " << task.cid << ", registering to waiting queue";
        pool->RegisterWaitingTask(task.cid, 
            [this, task](std::shared_ptr<NSMySQLConnection> conn) {
                ExecuteQueryWithConnection(task, conn);
            });
        return;
    }
    
    // 커넥션을 획득했으면 바로 실행
    ExecuteQueryWithConnection(task, conn);
}

void NSDataBaseManager::ExecuteQueryWithConnection(const Database::QueryTask& task, 
    std::shared_ptr<NSMySQLConnection> conn)
{
    // 연결 풀 다시 가져오기 (반환용)
    auto* pool = GetConnectionPool(static_cast<int32>(task.dbType));
    if (!pool)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get connection pool")));
        return;
    }
    
    Database::AsyncQueryTask asyncTask;
    asyncTask.cid = task.cid;
    asyncTask.connection = conn;
    asyncTask.procedureName = task.procedureName;
    
    // 타이머 생성 (RAII로 자동 측정)
    auto timer = std::make_shared<Database::QueryTimer>(task.queryData, m_afterExecuteQueryShared);
    
    // 성능 모니터링 시작
    DBPerformanceMonitor::GetInstance().RecordQueryStart(task.cid, 
        task.executeFunc ? "CALL " + task.procedureName + "(...)" : "CALL " + task.procedureName + "()");
    
    // 게임 스레드 포스트 함수만 캡처 (함수 포인터는 안전)
    auto gameThreadPost = m_gameThreadPost;
    asyncTask.callback = [gameThreadPost, task, conn, pool, timer](bool success, MYSQL_RES* result) {
        // 성능 모니터링 완료
        DBPerformanceMonitor::GetInstance().RecordQueryComplete(task.cid, success);
        
        // 타이머 소멸 시 자동으로 elapsed time 설정 및 콜백 호출
        timer.reset();
        
        if (success)
        {
            // RecordSet 생성
            task.queryData->SetResult(result);
            
            // 게임 스레드로 전달하여 promise 완료
            auto* instance = NSDataBaseManager::GetInstance();
            if (gameThreadPost && instance && !instance->m_isShuttingDown.load())
            {
                gameThreadPost([task]() mutable {
                    // 게임 스레드에서 promise 완료 → Then 콜백도 게임 스레드에서 실행
                    task.promise.SetValue(task.queryData);
                });
            }
            else
            {
                // 디스패처 없으면 에러 처리
                LOGE << "CRITICAL: Game thread dispatcher not available for query callback!";
                task.queryData->SetError("Game thread dispatcher not available");
                task.promise.SetException(std::make_exception_ptr(
                    std::runtime_error("DB callback failed: Game thread dispatcher not set")));
            }
        }
        else
        {
            // 에러도 게임 스레드에서 처리
            if (gameThreadPost && !isShuttingDown->load())
            {
                gameThreadPost([task]() mutable {
                    task.promise.SetException(std::make_exception_ptr(
                        std::runtime_error("Query execution failed")));
                });
            }
            else
            {
                // 디스패처 없어도 에러는 전달해야 함
                LOGE << "CRITICAL: Game thread dispatcher not available for error callback!";
                task.promise.SetException(std::make_exception_ptr(
                    std::runtime_error("Query failed and dispatcher not available")));
            }
        }
        
        // 커넥션 반환
        pool->ReturnConnection(std::move(conn));
        
        // 다음 작업 확인
        CheckNextTaskForCID(task.cid);
    };
    
    // 쿼리 실행 준비
    if (task.executeFunc)
    {
        // NSMySQLConnection의 기존 PreparedStatement 캐싱 시스템 활용
        // 프로시저 메타데이터 가져오기 (이미 캐싱됨)
        auto metadata = conn->GetProcedureMetadata(task.procedureName);
        if (!metadata)
        {
            // 메타데이터가 없으면 로드 시도
            if (!conn->LoadProcedureMetadata(task.procedureName))
            {
                task.promise.SetException(std::make_exception_ptr(
                    std::runtime_error("Failed to load procedure metadata for " + task.procedureName)));
                pool->ReturnConnection(std::move(conn));
                CheckNextTaskForCID(task.cid);
                return;
            }
            metadata = conn->GetProcedureMetadata(task.procedureName);
        }
        
        // CALL 쿼리 생성
        std::stringstream ss;
        ss << "CALL " << task.procedureName << "(";
        for (size_t i = 0; i < metadata->parameters.size(); ++i)
        {
            if (i > 0) ss << ", ";
            ss << "?";
        }
        ss << ")";
        std::string callQuery = ss.str();
        
        // 캐시된 PreparedStatement 가져오기
        auto stmtPtr = conn->GetCachedStatement(callQuery);
        if (!stmtPtr)
        {
            task.promise.SetException(std::make_exception_ptr(
                std::runtime_error("Failed to get cached statement for " + task.procedureName)));
            pool->ReturnConnection(std::move(conn));
            CheckNextTaskForCID(task.cid);
            return;
        }
        
        // MySQLCommand를 사용하여 파라미터 바인딩
        auto command = std::make_unique<MySQLCommand>();
        
        // NSStoredProcedure의 MakeQuery를 호출해야 하지만
        // 현재는 간단히 빈 command로 진행 (실제로는 StoredProcedure 인스턴스가 필요)
        std::vector<MYSQL_BIND> binds;  // MySQL C API 호환성
        command->BindToMySQL(stmtPtr.get(), binds);
        
        asyncTask.isStmtQuery = true;
        asyncTask.stmt = stmtPtr;  // shared_ptr 직접 할당
        asyncTask.query = callQuery;
        asyncTask.procedureName = task.procedureName;
        asyncTask.parameterCount = static_cast<int>(metadata->parameters.size());
    }
    else
    {
        // 일반 쿼리 실행 (프로시저 직접 호출)
        asyncTask.query = "CALL " + task.procedureName + "()";
    }
    
    // 비동기 쿼리 폴러에 추가
    m_asyncQueryPoller->AddQuery(std::move(asyncTask));
}

// PollActiveQueries는 AsyncQueryPoller로 이동됨

void NSDataBaseManager::ProcessCompletedQuery(Database::AsyncQueryTask& asyncTask)
{
    m_outputCount++;
    m_queriesProcessing--;
    
    if (asyncTask.callback)
    {
        bool success = (asyncTask.state == Database::AsyncQueryState::Completed);
        
        // 디버그 로깅
        if (!success)
        {
            LOGE << "Query failed for CID " << asyncTask.cid 
                 << ", procedure: " << asyncTask.procedureName
                 << ", error: " << asyncTask.errorMessage;
        }
        
        try
        {
            asyncTask.callback(success, asyncTask.result.get());
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception in query callback for CID " << asyncTask.cid << ": " << e.what();
        }
    }
    
    // 결과는 콜백에서 처리했으므로 여기서는 정리하지 않음
    // asyncTask.result는 콜백에서 NSQueryData에 이동됨
}

void NSDataBaseManager::CheckNextTaskForCID(int64_t cid)
{
    // CID에 대기 중인 작업이 있는지 확인
    if (!m_cidQueueManager->HasPendingTasks(cid))
    {
        return;  // 작업이 없음
    }
    
    // 다음 작업의 DB 타입 가져오기 (피크만)
    Database::QueryTask peekTask;
    if (!m_cidQueueManager->DequeueTask(cid, peekTask))
    {
        return;
    }
    
    EDataBase dbType = peekTask.dbType;
    
    // 다시 큐에 넣기 (피크했던 작업)
    m_cidQueueManager->EnqueueTask(cid, std::move(peekTask));
    
    LOGD << "CID " << cid << " has more tasks, registering to connection queue";
    
    auto* pool = GetConnectionPool(static_cast<int32>(dbType));
    if (pool)
    {
        pool->RegisterWaitingTask(cid,
            [this, cid](std::shared_ptr<NSMySQLConnection> conn) {
                ProcessCIDQueueWithConnection(cid, conn);
            });
    }
}

void NSDataBaseManager::ProcessCIDQueueWithConnection(int64_t cid, std::shared_ptr<NSMySQLConnection> conn)
{
    // CID 큐 매니저에서 다음 작업 가져오기
    Database::QueryTask task;
    if (!m_cidQueueManager->DequeueTask(cid, task))
    {
        // 작업이 없으면 커넥션 반환
        auto* pool = GetConnectionPool(static_cast<int32>(EDataBase::Game));
        if (pool)
        {
            pool->ReturnConnection(conn);
        }
        return;
    }
    
    // 커넥션으로 작업 실행
    ExecuteQueryWithConnection(task, conn);
}

void NSDataBaseManager::PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData)
{
    // 더 이상 사용되지 않음 - promise.SetValue()가 게임 스레드에서 실행되므로
    // Then 콜백이 자동으로 게임 스레드에서 실행됨
}

// PostWork와 WorkerThreadFunc는 WorkerThreadPool로 이동됨

bool NSDataBaseManager::LoadAllProcedureMetadata()
{
    LOGI << "Loading all procedure metadata...";
    
    // 첫 번째 게임 DB 연결 가져오기
    auto* pool = GetConnectionPool(static_cast<int32>(EDataBase::Game));
    if (!pool)
    {
        LOGE << "Failed to get connection pool for procedure metadata loading";
        return false;
    }
    
    auto conn = pool->GetConnection();
    if (!conn)
    {
        LOGE << "Failed to get connection for procedure metadata loading";
        return false;
    }
    
    // 모든 프로시저의 파라미터 정보를 한 번에 조회
    // GAME, GAME_TEST, GAME_DEV 등이 있어도 프로시저 구조는 동일하므로
    // 하나의 스키마에서만 가져오면 됨
    std::string query = R"(
        SELECT SPECIFIC_NAME, PARAMETER_NAME, DATA_TYPE, PARAMETER_MODE, ORDINAL_POSITION
        FROM information_schema.PARAMETERS
        WHERE SPECIFIC_SCHEMA = DATABASE()
        AND ROUTINE_TYPE = 'PROCEDURE'
        ORDER BY SPECIFIC_NAME, ORDINAL_POSITION
    )";
    
    if (!conn->ExecuteQuery(query))
    {
        LOGE << "Failed to query procedure metadata";
        pool->ReturnConnection(std::move(conn));
        return false;
    }
    
    auto result = conn->GetRecordSet();
    if (!result)
    {
        LOGE << "Failed to get result set for procedure metadata";
        pool->ReturnConnection(std::move(conn));
        return false;
    }
    
    // 결과를 프로시저별로 그룹화
    std::string currentProc;
    std::shared_ptr<NSMySQLConnection::ProcedureMetadata> currentMetadata;
    
    while (!result->IsEOF())
    {
        std::string procName;
        std::string paramName;
        std::string dataType;
        std::string paramMode;
        int ordinalPosition;
        
        result->GetItem("SPECIFIC_NAME", procName);
        result->GetItem("PARAMETER_NAME", paramName);
        result->GetItem("DATA_TYPE", dataType);
        result->GetItem("PARAMETER_MODE", paramMode);
        result->GetItem("ORDINAL_POSITION", ordinalPosition);
        
        // 새로운 프로시저 시작
        if (procName != currentProc)
        {
            if (currentMetadata && !currentProc.empty())
            {
                // 이전 프로시저 저장
                std::unique_lock<std::shared_mutex> lock(s_globalMetadataMutex);
                s_globalMetadataCache[currentProc] = currentMetadata;
            }
            
            currentProc = procName;
            currentMetadata = std::make_shared<NSMySQLConnection::ProcedureMetadata>();
        }
        
        // 파라미터 정보 추가
        if (!paramName.empty())  // 파라미터가 없는 프로시저도 있을 수 있음
        {
            NSMySQLConnection::ProcedureMetadata::Parameter param;
            param.name = paramName;
            
            // 데이터 타입 매핑
            if (dataType == "int" || dataType == "integer") 
                param.type = MYSQL_TYPE_LONG;
            else if (dataType == "bigint") 
                param.type = MYSQL_TYPE_LONGLONG;
            else if (dataType == "varchar" || dataType == "char" || dataType == "text") 
                param.type = MYSQL_TYPE_VAR_STRING;
            else if (dataType == "datetime" || dataType == "timestamp") 
                param.type = MYSQL_TYPE_DATETIME;
            else if (dataType == "float") 
                param.type = MYSQL_TYPE_FLOAT;
            else if (dataType == "double") 
                param.type = MYSQL_TYPE_DOUBLE;
            else if (dataType == "tinyint") 
                param.type = MYSQL_TYPE_TINY;
            else if (dataType == "smallint") 
                param.type = MYSQL_TYPE_SHORT;
            else 
                param.type = MYSQL_TYPE_STRING;
            
            // 파라미터 모드
            param.isOutput = (paramMode == "OUT" || paramMode == "INOUT");
            
            currentMetadata->parameters.push_back(param);
        }
    }
    
    // 마지막 프로시저 저장
    if (currentMetadata && !currentProc.empty())
    {
        std::unique_lock<std::shared_mutex> lock(s_globalMetadataMutex);
        s_globalMetadataCache[currentProc] = currentMetadata;
    }
    
    pool->ReturnConnection(std::move(conn));
    
    LOGI << "Loaded metadata for " << s_globalMetadataCache.size() << " procedures";
    return true;
}

std::shared_ptr<NSMySQLConnection::ProcedureMetadata> NSDataBaseManager::GetGlobalProcedureMetadata(const std::string& procName)
{
    std::shared_lock<std::shared_mutex> lock(s_globalMetadataMutex);
    auto it = s_globalMetadataCache.find(procName);
    if (it != s_globalMetadataCache.end())
        return it->second;
    return nullptr;
}

// Explicit template instantiations (if needed)
// template DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl<SomeSpecificProcedure>(...);