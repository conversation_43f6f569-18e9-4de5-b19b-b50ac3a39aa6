#pragma once

#include <unordered_map>
#include <string>
#include <memory>

#include "NPErrorCode.h"
#include "Common/QueryDataTypes.h"

constexpr uint32_t g_uMaxQueryRecordSetCount = 64;

class MySQLCommand;
class RecordSet;
class NSDataSerializer;
class NSPQueryData
{
public:
	using MAP_RECORDSET = QueryDataTypes::MAP_RECORDSET;
	using MAP_RETURNVALUE = QueryDataTypes::MAP_RETURNVALUE;
	using MAP_COMMANDNAME = QueryDataTypes::MAP_COMMANDNAME;

public:
	NSPQueryData();
	virtual ~NSPQueryData();
	void Reset();

public:
	NSDataSerializer& GetQueryData();
	RecordSet* GetRecordSet();
	RecordSet* GetRecordSet(int iIndex);
	RecordSet* GetRecordSet(const char* strCommandName);
	virtual void RunProcQuery() = 0;
	virtual void RunResultQuery() = 0;

	void SetRecordSet(std::string strCommandName, RecordSet* pcRecordSet);
	bool SetCommand(std::unique_ptr<MySQLCommand> pcCommand);
	void SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue);
	MySQLCommand* GetCommand(int iIndex = 0);
	const char* GetCommandName(int iIndex = 0);

	const int GetQuerySetCount() const { return m_iQuerySetCount; }

	int GetReturnValue();
	int GetReturnValue(int iIndex);
	int GetReturnValue(const char* strCommandName);

	void SetErrorCode(const EErrorCode eErrorCode);
	EErrorCode GetErrorCode() const;
	bool IsValid() const;

	void SetAllocFuncName(const wchar_t* pAllocFuncName) { m_pAllocFuncName = pAllocFuncName; }
	const wchar_t* GetAllocFuncName() { return m_pAllocFuncName; }

	void SetAllocLine(int iAllocLine) { m_iAllocLine = iAllocLine; }
	int GetAllocLine() const { return m_iAllocLine; }

	void SetElapsedTime(std::chrono::high_resolution_clock::duration elapsedTime);
	auto GetElapsedTime() const->std::chrono::high_resolution_clock::duration;

protected:
	std::unique_ptr<NSDataSerializer> m_pcDataSerializer;
	std::unique_ptr<MySQLCommand> m_pcCommand[g_uMaxQueryRecordSetCount];
	std::unique_ptr<RecordSet> m_pcRecordset[g_uMaxQueryRecordSetCount];
	int m_iReturnValue[g_uMaxQueryRecordSetCount];
	EErrorCode m_eErrorCode;

	MAP_RECORDSET m_mapRecordsetByName;
	MAP_RETURNVALUE m_mapReturnValueByName;
	MAP_COMMANDNAME m_mapCommandNameByIdx;

	int m_iQuerySetCount = 0;

	const wchar_t* m_pAllocFuncName;
	int m_iAllocLine;

	std::chrono::high_resolution_clock::duration m_ElapsedTime{};
};