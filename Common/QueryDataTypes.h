#pragma once

#include <unordered_map>
#include <string>

// Forward declarations
class RecordSet;

// 쿼리 데이터 관련 공통 타입 정의
namespace QueryDataTypes 
{
    // RecordSet 매핑 타입 - 프로시저 이름으로 RecordSet을 찾기 위한 맵
    using MAP_RECORDSET = std::unordered_map<std::string, RecordSet*>;
    
    // 반환값 매핑 타입 - 프로시저 이름으로 반환값을 저장하는 맵
    using MAP_RETURNVALUE = std::unordered_map<std::string, int>;
    
    // 커맨드 이름 매핑 타입 - 인덱스로 커맨드 이름을 찾기 위한 맵
    using MAP_COMMANDNAME = std::unordered_map<int, std::string>;
}