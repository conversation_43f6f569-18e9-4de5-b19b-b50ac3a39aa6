#pragma once
#include <string>
#include <memory>
#include <vector>
#include <cstdint>

// 레거시 호환 매크로
#define DECLARE_MODEL_UTIL \
public: \
	virtual const int32_t GetModelId() const override { return GetTypeId(); } \
	virtual const char* GetModelName() const override { return strComponentName; } \
	virtual const char* GetSelectName() const override { return spSelect; } \
	virtual const char* GetUpsertName() const override { return spUpsert; }

#define DECLARE_ESCROW_UTIL \
public: \
	virtual const char* GetEscrowDepositName() const override { return spDeposit; } \
	virtual const char* GetEscrowWithdrawName() const override { return spWithdraw; } \
	virtual const char* GetEscrowReclaimName() const override { return spReclaim; }

// OUT 매크로 정의
#ifndef OUT
#define OUT
#endif

// 스토리지 모델 기본 클래스
class NSStorageModel
{
public:
    virtual ~NSStorageModel() = default;
    
    // 레거시 인터페이스
    virtual auto GetModelId() const->const int32_t = 0;
    virtual auto GetModelName() const->const char* = 0;
    virtual auto GetSelectName() const->const char* = 0;
    virtual auto GetUpsertName() const->const char* = 0;
    virtual void Serialize(OUT std::vector<std::string>& payloads) = 0;
    virtual void SerializeAffected(OUT std::vector<std::string>& payloads) = 0;
    virtual void Rollback() = 0;
    
    // 추가 메서드 (새 기능과 하위 호환성)
    virtual int64_t GetAid() const { return m_aid; }
    virtual int64_t GetCid() const { return m_cid; }
    virtual void SetAid(int64_t aid) { m_aid = aid; }
    virtual void SetCid(int64_t cid) { m_cid = cid; }
    
    // 변경 추적
    bool IsDirty() const { return m_isDirty; }
    void SetDirty(bool dirty) { m_isDirty = dirty; }
    void MarkDirty() { m_isDirty = true; }
    
    // SQL 생성 (새 기능)
    virtual std::string GenerateInsertSQL() const { return ""; }
    virtual std::string GenerateUpdateSQL() const { return ""; }
    virtual std::string GenerateDeleteSQL() const { return ""; }
    virtual std::string GenerateSelectSQL() const { return ""; }
    
    // 시퀀스 번호 (순차적 업데이트용)
    int64_t GetSequence() const { return m_sequence; }
    void SetSequence(int64_t seq) { m_sequence = seq; }
    
    // 복제 및 복원
    virtual std::shared_ptr<NSStorageModel> Clone() const { return nullptr; }
    virtual void RestoreFrom(NSStorageModel* other) {}
    
protected:
    int64_t m_aid = 0;
    int64_t m_cid = 0;
    int64_t m_sequence = 0;
    bool m_isDirty = false;
};

// Escrow 모델 (에스크로 거래용)
class NSStorageEscrowModel : public NSStorageModel
{
public:
    virtual auto GetEscrowDepositName() const->const char* = 0;
    virtual auto GetEscrowWithdrawName() const->const char* = 0;
    virtual auto GetEscrowReclaimName() const->const char* = 0;
    virtual void SerializeEscrow(OUT std::string& payload, const int64_t receiverCid) = 0;
};